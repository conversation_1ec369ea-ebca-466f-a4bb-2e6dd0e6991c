#!/usr/bin/env python
"""
专门测试start_session功能
"""
import asyncio
import logging
from fastmcp import Client
from fastmcp.client.transports import SSETransport
import json

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("session_test")

async def call_start_session():
    """调用start_session工具"""
    server_url = "http://localhost:8000/mcp"
    
    # 创建客户端连接
    transport = SSETransport(url=server_url)
    client = Client(transport)
    
    try:
        # 连接服务并调用start_session
        async with client:
            logger.info("已连接到MCP服务")
            
            # 直接调用start_session
            logger.info("调用start_session...")
            result = await client.call_tool("start_session", {})
            
            if result and hasattr(result.content[0], 'text'):
                # 尝试解析JSON响应
                content = result.content[0].text
                logger.info(f"原始响应: {content}")
                
                try:
                    data = json.loads(content)
                    logger.info(f"解析后的响应: {data}")
                    
                    # 检查是否有session_id
                    if "session_id" in data:
                        logger.info(f"✓ 成功获取会话ID: {data['session_id']}")
                    else:
                        # 检查是否有错误信息
                        if "error" in data:
                            logger.error(f"✗ 创建会话失败: {data['error']}")
                        else:
                            logger.error(f"✗ 响应中没有session_id: {data}")
                except json.JSONDecodeError:
                    logger.error(f"✗ 无法解析JSON响应: {content}")
            else:
                logger.error(f"✗ 未获取到文本响应: {result}")
                
            logger.info("调用query_knowledge_graph...")
            
            # result = await client.call_tool("query_knowledge_graph", {
            # "session_id": data['session_id'],
            # "original_user_query_text": "生成的随机字符串",
            # "query_text": "注释注释注释注释注释注释",
            # "repo_id": "gradle-test",
            # "top_k": 3
            # })
            #
            # result = await client.call_tool("get_node_content", {
            # "session_id": data['session_id'],
            # "node_id": "86095cda26f3b1467556aa5d93a05de5",
            # "repo_id": "**************:diodeme/lilypadoc.git"
            # })

            result = await client.call_tool("get_node_content", {
                "session_id": data['session_id'],
                "node_id": "86095cda26f3b1467556aa5d93a05de5",
                "repo_id": "**************:diodeme/lilypadoc.git"
            })

            result = await client.call_tool("close_session", {
            "session_id": data['session_id']
            })
    except Exception as e:
        logger.error(f"✗ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    return False

if __name__ == "__main__":
    logger.info("开始测试start_session功能...")
    asyncio.run(call_start_session()) 