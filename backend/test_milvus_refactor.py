#!/usr/bin/env python3
"""
测试Milvus服务重构后的功能
验证新的配置方式和向后兼容性
"""

import sys
import os
from pathlib import Path

# 添加backend目录到系统路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_imports():
    """测试导入功能"""
    print("🔍 测试导入功能...")
    
    try:
        from app.service.milvus_service import (
            MilvusService, 
            MilvusConnectionConfig, 
            MilvusFieldMapping,
            MilvusFields,  # 向后兼容
            create_milvus_service_from_config,
            create_default_milvus_service
        )
        print("✅ 所有类和函数导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_config_classes():
    """测试配置类"""
    print("\n🔍 测试配置类...")
    
    try:
        from app.service.milvus_service import MilvusConnectionConfig, MilvusFieldMapping
        
        # 测试连接配置
        config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            token="test_token",
            collection_name="test_collection",
            dimension=768
        )
        print(f"✅ 连接配置创建成功: {config}")
        
        # 测试字段映射
        field_mapping = MilvusFieldMapping(
            id_field="custom_id",
            text_dense="custom_dense"
        )
        print(f"✅ 字段映射创建成功: {field_mapping}")
        
        # 测试从配置管理器创建
        try:
            config_from_manager = MilvusConnectionConfig.from_config_manager()
            print(f"✅ 从配置管理器创建成功: {config_from_manager}")
        except Exception as e:
            print(f"⚠️  从配置管理器创建失败（可能是配置文件问题）: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置类测试失败: {e}")
        return False


def test_service_creation():
    """测试服务创建"""
    print("\n🔍 测试服务创建...")
    
    try:
        from app.service.milvus_service import (
            MilvusService, 
            MilvusConnectionConfig, 
            MilvusFieldMapping,
            create_milvus_service_from_config,
            create_default_milvus_service
        )
        
        # 测试1: 默认构造函数（向后兼容）
        service1 = MilvusService()
        print("✅ 默认构造函数创建成功")
        
        # 测试2: 使用配置对象
        config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="test_collection"
        )
        service2 = MilvusService(connection_config=config)
        print("✅ 使用配置对象创建成功")
        
        # 测试3: 使用配置对象和字段映射
        field_mapping = MilvusFieldMapping(id_field="custom_id")
        service3 = MilvusService(connection_config=config, field_mapping=field_mapping)
        print("✅ 使用配置对象和字段映射创建成功")
        
        # 测试4: 便利函数
        service4 = create_default_milvus_service()
        print("✅ 便利函数创建成功")
        
        try:
            service5 = create_milvus_service_from_config()
            print("✅ 从配置管理器创建成功")
        except Exception as e:
            print(f"⚠️  从配置管理器创建失败（可能是配置文件问题）: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务创建测试失败: {e}")
        return False


def test_field_mapping():
    """测试新的灵活字段映射功能"""
    print("\n🔍 测试字段映射功能...")

    try:
        from app.service.milvus_service import MilvusService, MilvusFieldMapping

        # 创建灵活的字段映射
        custom_mapping = MilvusFieldMapping(
            id_field="my_id",
            dense_vector_field="my_dense_vector",
            sparse_vector_field="my_sparse_vector",
            content_field="my_content",
            metadata_fields={
                "title": "doc_title",
                "category": "doc_category"
            },
            custom_fields={
                "priority": "task_priority",
                "status": "task_status"
            }
        )

        service = MilvusService(field_mapping=custom_mapping)

        # 验证核心字段
        assert service.field_mapping.id_field == "my_id"
        assert service.field_mapping.dense_vector_field == "my_dense_vector"
        assert service.field_mapping.sparse_vector_field == "my_sparse_vector"
        assert service.field_mapping.content_field == "my_content"

        # 验证字段访问方法
        assert service.field_mapping.get_field("id") == "my_id"
        assert service.field_mapping.get_field("dense_vector") == "my_dense_vector"
        assert service.field_mapping.get_field("content") == "my_content"
        assert service.field_mapping.get_field("title") == "doc_title"
        assert service.field_mapping.get_field("priority") == "task_priority"

        # 测试动态添加字段
        service.field_mapping.set_field("new_field", "actual_new_field")
        assert service.field_mapping.get_field("new_field") == "actual_new_field"

        # 测试获取所有字段
        all_fields = service.field_mapping.get_all_fields()
        assert "id" in all_fields
        assert "title" in all_fields
        assert "priority" in all_fields
        assert "new_field" in all_fields

        print("✅ 字段映射功能正常")
        return True

    except Exception as e:
        print(f"❌ 字段映射测试失败: {e}")
        return False


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔍 测试向后兼容性...")
    
    try:
        from app.service.milvus_service import MilvusFields
        
        # 验证旧的字段常量仍然可用
        assert MilvusFields.ID_FIELD == "id"
        assert MilvusFields.TEXT_DENSE == "text_dense"
        assert MilvusFields.NODE_TYPE_FIELD == "node_type"
        
        print("✅ 向后兼容性正常")
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False


def test_different_collections():
    """测试不同collection的配置"""
    print("\n🔍 测试不同collection的配置...")

    try:
        from app.service.milvus_service import MilvusService, MilvusConnectionConfig, MilvusFieldMapping

        # 文档collection配置
        doc_config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="documents",
            dimension=768
        )

        doc_fields = MilvusFieldMapping(
            id_field="doc_id",
            dense_vector_field="text_embedding",
            content_field="document_content",
            metadata_fields={
                "title": "doc_title",
                "author": "doc_author",
                "category": "doc_category"
            }
        )

        doc_service = MilvusService(connection_config=doc_config, field_mapping=doc_fields)
        print("✅ 文档collection服务创建成功")

        # 代码collection配置
        code_config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="code_snippets",
            dimension=1024
        )

        code_fields = MilvusFieldMapping(
            id_field="code_id",
            dense_vector_field="code_embedding",
            sparse_vector_field="keyword_embedding",
            content_field="source_code",
            custom_fields={
                "language": "programming_language",
                "repo": "repository_name",
                "file_path": "file_location"
            }
        )

        code_service = MilvusService(connection_config=code_config, field_mapping=code_fields)
        print("✅ 代码collection服务创建成功")

        # 验证不同服务的字段配置
        assert doc_service.field_mapping.get_field("id") == "doc_id"
        assert doc_service.field_mapping.get_field("dense_vector") == "text_embedding"
        assert doc_service.field_mapping.get_field("title") == "doc_title"

        assert code_service.field_mapping.get_field("id") == "code_id"
        assert code_service.field_mapping.get_field("dense_vector") == "code_embedding"
        assert code_service.field_mapping.get_field("language") == "programming_language"

        print("✅ 不同collection配置验证成功")
        return True

    except Exception as e:
        print(f"❌ 不同collection配置测试失败: {e}")
        return False


def test_knowledge_graph_service():
    """测试知识图谱服务更新"""
    print("\n🔍 测试知识图谱服务更新...")

    try:
        from app.service.knowledge_graph_service import KnowledgeGraphService
        from app.service.milvus_service import MilvusConnectionConfig, MilvusFieldMapping

        # 测试1: 默认构造函数
        kg_service1 = KnowledgeGraphService()
        print("✅ 知识图谱服务默认构造函数创建成功")

        # 测试2: 使用自定义Milvus配置
        config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="test_collection"
        )
        field_mapping = MilvusFieldMapping(
            id_field="custom_id",
            dense_vector_field="custom_vector",
            metadata_fields={"type": "node_type"}
        )

        kg_service2 = KnowledgeGraphService(
            milvus_connection_config=config,
            milvus_field_mapping=field_mapping
        )
        print("✅ 知识图谱服务自定义配置创建成功")

        return True

    except Exception as e:
        print(f"❌ 知识图谱服务测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 测试Milvus服务重构")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config_classes,
        test_service_creation,
        test_field_mapping,
        test_different_collections,
        test_backward_compatibility,
        test_knowledge_graph_service
    ]
    
    success_count = 0
    total_tests = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                success_count += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！重构成功！")
    else:
        print("⚠️  部分测试失败，请检查代码")
    
    return success_count == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
