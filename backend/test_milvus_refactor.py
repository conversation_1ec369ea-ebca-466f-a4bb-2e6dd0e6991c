#!/usr/bin/env python3
"""
测试Milvus服务重构后的功能
验证新的配置方式和向后兼容性
"""

import sys
import os
from pathlib import Path

# 添加backend目录到系统路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_imports():
    """测试导入功能"""
    print("🔍 测试导入功能...")
    
    try:
        from app.service.milvus_service import (
            MilvusService,
            MilvusConnectionConfig,
            MilvusFieldConfig
        )
        print("✅ 所有类和函数导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_config_classes():
    """测试配置类"""
    print("\n🔍 测试配置类...")
    
    try:
        from app.service.milvus_service import MilvusConnectionConfig, MilvusFieldConfig

        # 测试连接配置
        config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            token="test_token",
            collection_name="test_collection",
            dimension=768
        )
        print(f"✅ 连接配置创建成功: {config}")

        # 测试字段配置
        field_config = MilvusFieldConfig(
            id_field="custom_id",
            dense_vector_field="custom_dense",
            output_fields=["custom_id", "content", "title"]
        )
        print(f"✅ 字段配置创建成功: {field_config}")

        return True

    except Exception as e:
        print(f"❌ 配置类测试失败: {e}")
        return False


def test_service_creation():
    """测试服务创建"""
    print("\n🔍 测试服务创建...")
    
    try:
        from app.service.milvus_service import (
            MilvusService,
            MilvusConnectionConfig,
            MilvusFieldConfig
        )

        # 测试1: 使用配置对象
        connection_config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="test_collection"
        )
        field_config = MilvusFieldConfig(
            id_field="test_id",
            dense_vector_field="test_vector",
            output_fields=["test_id", "content"]
        )
        service1 = MilvusService(connection_config=connection_config, field_config=field_config)
        print("✅ 使用配置对象创建成功")

        # 测试2: 不同的字段配置
        field_config2 = MilvusFieldConfig(
            id_field="custom_id",
            dense_vector_field="custom_vector",
            sparse_vector_field="custom_sparse",
            output_fields=["custom_id", "title", "content", "category"]
        )
        service2 = MilvusService(connection_config=connection_config, field_config=field_config2)
        print("✅ 使用不同字段配置创建成功")

        return True

    except Exception as e:
        print(f"❌ 服务创建测试失败: {e}")
        return False


def test_field_config():
    """测试字段配置功能"""
    print("\n🔍 测试字段配置功能...")

    try:
        from app.service.milvus_service import MilvusService, MilvusConnectionConfig, MilvusFieldConfig

        # 创建连接配置
        connection_config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="test_collection"
        )

        # 创建字段配置
        field_config = MilvusFieldConfig(
            id_field="my_id",
            dense_vector_field="my_dense_vector",
            sparse_vector_field="my_sparse_vector",
            output_fields=["my_id", "content", "title", "category"]
        )

        service = MilvusService(connection_config=connection_config, field_config=field_config)

        # 验证字段配置
        assert service.field_config.id_field == "my_id"
        assert service.field_config.dense_vector_field == "my_dense_vector"
        assert service.field_config.sparse_vector_field == "my_sparse_vector"
        assert "my_id" in service.field_config.output_fields
        assert "content" in service.field_config.output_fields

        print("✅ 字段配置功能正常")
        return True

    except Exception as e:
        print(f"❌ 字段配置测试失败: {e}")
        return False


def test_required_parameters():
    """测试必需参数验证"""
    print("\n🔍 测试必需参数验证...")

    try:
        from app.service.milvus_service import MilvusService, MilvusConnectionConfig, MilvusFieldConfig

        # 测试必需参数
        connection_config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="test_collection"
        )

        field_config = MilvusFieldConfig(
            id_field="test_id",
            output_fields=["test_id"]
        )

        # 应该能够成功创建
        service = MilvusService(connection_config=connection_config, field_config=field_config)
        assert service.connection_config.uri == "http://localhost:19530"
        assert service.field_config.id_field == "test_id"

        print("✅ 必需参数验证正常")
        return True

    except Exception as e:
        print(f"❌ 必需参数验证测试失败: {e}")
        return False


def test_different_collections():
    """测试不同collection的配置"""
    print("\n🔍 测试不同collection的配置...")

    try:
        from app.service.milvus_service import MilvusService, MilvusConnectionConfig, MilvusFieldConfig

        # 文档collection配置
        doc_config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="documents",
            dimension=768
        )

        doc_fields = MilvusFieldConfig(
            id_field="doc_id",
            dense_vector_field="text_embedding",
            output_fields=["doc_id", "document_content", "doc_title", "doc_author", "doc_category"]
        )

        doc_service = MilvusService(connection_config=doc_config, field_config=doc_fields)
        print("✅ 文档collection服务创建成功")

        # 代码collection配置
        code_config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="code_snippets",
            dimension=1024
        )

        code_fields = MilvusFieldConfig(
            id_field="code_id",
            dense_vector_field="code_embedding",
            sparse_vector_field="keyword_embedding",
            output_fields=["code_id", "source_code", "programming_language", "repository_name", "file_location"]
        )

        code_service = MilvusService(connection_config=code_config, field_config=code_fields)
        print("✅ 代码collection服务创建成功")

        # 验证不同服务的字段配置
        assert doc_service.field_config.id_field == "doc_id"
        assert doc_service.field_config.dense_vector_field == "text_embedding"
        assert "doc_title" in doc_service.field_config.output_fields

        assert code_service.field_config.id_field == "code_id"
        assert code_service.field_config.dense_vector_field == "code_embedding"
        assert code_service.field_config.sparse_vector_field == "keyword_embedding"
        assert "programming_language" in code_service.field_config.output_fields

        print("✅ 不同collection配置验证成功")
        return True

    except Exception as e:
        print(f"❌ 不同collection配置测试失败: {e}")
        return False


def test_knowledge_graph_service():
    """测试知识图谱服务更新"""
    print("\n🔍 测试知识图谱服务更新...")

    try:
        from app.service.knowledge_graph_service import KnowledgeGraphService
        from app.service.milvus_service import MilvusConnectionConfig, MilvusFieldMapping

        # 测试1: 默认构造函数
        kg_service1 = KnowledgeGraphService()
        print("✅ 知识图谱服务默认构造函数创建成功")

        # 测试2: 使用自定义Milvus配置
        config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="test_collection"
        )
        field_mapping = MilvusFieldMapping(
            id_field="custom_id",
            dense_vector_field="custom_vector",
            metadata_fields={"type": "node_type"}
        )

        kg_service2 = KnowledgeGraphService(
            milvus_connection_config=config,
            milvus_field_mapping=field_mapping
        )
        print("✅ 知识图谱服务自定义配置创建成功")

        return True

    except Exception as e:
        print(f"❌ 知识图谱服务测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 测试Milvus服务重构")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config_classes,
        test_service_creation,
        test_field_mapping,
        test_different_collections,
        test_backward_compatibility,
        test_knowledge_graph_service
    ]
    
    success_count = 0
    total_tests = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                success_count += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！重构成功！")
    else:
        print("⚠️  部分测试失败，请检查代码")
    
    return success_count == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
