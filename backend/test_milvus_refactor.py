#!/usr/bin/env python3
"""
测试Milvus服务重构后的功能
验证新的配置方式和向后兼容性
"""

import sys
import os
from pathlib import Path

# 添加backend目录到系统路径
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_imports():
    """测试导入功能"""
    print("🔍 测试导入功能...")
    
    try:
        from app.service.milvus_service import (
            MilvusService, 
            MilvusConnectionConfig, 
            MilvusFieldMapping,
            MilvusFields,  # 向后兼容
            create_milvus_service_from_config,
            create_default_milvus_service
        )
        print("✅ 所有类和函数导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_config_classes():
    """测试配置类"""
    print("\n🔍 测试配置类...")
    
    try:
        from app.service.milvus_service import MilvusConnectionConfig, MilvusFieldMapping
        
        # 测试连接配置
        config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            token="test_token",
            collection_name="test_collection",
            dimension=768
        )
        print(f"✅ 连接配置创建成功: {config}")
        
        # 测试字段映射
        field_mapping = MilvusFieldMapping(
            id_field="custom_id",
            text_dense="custom_dense"
        )
        print(f"✅ 字段映射创建成功: {field_mapping}")
        
        # 测试从配置管理器创建
        try:
            config_from_manager = MilvusConnectionConfig.from_config_manager()
            print(f"✅ 从配置管理器创建成功: {config_from_manager}")
        except Exception as e:
            print(f"⚠️  从配置管理器创建失败（可能是配置文件问题）: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置类测试失败: {e}")
        return False


def test_service_creation():
    """测试服务创建"""
    print("\n🔍 测试服务创建...")
    
    try:
        from app.service.milvus_service import (
            MilvusService, 
            MilvusConnectionConfig, 
            MilvusFieldMapping,
            create_milvus_service_from_config,
            create_default_milvus_service
        )
        
        # 测试1: 默认构造函数（向后兼容）
        service1 = MilvusService()
        print("✅ 默认构造函数创建成功")
        
        # 测试2: 使用配置对象
        config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="test_collection"
        )
        service2 = MilvusService(connection_config=config)
        print("✅ 使用配置对象创建成功")
        
        # 测试3: 使用配置对象和字段映射
        field_mapping = MilvusFieldMapping(id_field="custom_id")
        service3 = MilvusService(connection_config=config, field_mapping=field_mapping)
        print("✅ 使用配置对象和字段映射创建成功")
        
        # 测试4: 便利函数
        service4 = create_default_milvus_service()
        print("✅ 便利函数创建成功")
        
        try:
            service5 = create_milvus_service_from_config()
            print("✅ 从配置管理器创建成功")
        except Exception as e:
            print(f"⚠️  从配置管理器创建失败（可能是配置文件问题）: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务创建测试失败: {e}")
        return False


def test_field_mapping():
    """测试字段映射功能"""
    print("\n🔍 测试字段映射功能...")
    
    try:
        from app.service.milvus_service import MilvusService, MilvusFieldMapping
        
        # 创建自定义字段映射
        custom_mapping = MilvusFieldMapping(
            id_field="my_id",
            text_dense="my_dense_vector",
            text_sparse="my_sparse_vector",
            node_type_field="my_node_type"
        )
        
        service = MilvusService(field_mapping=custom_mapping)
        
        # 验证字段映射是否正确设置
        assert service.field_mapping.id_field == "my_id"
        assert service.field_mapping.text_dense == "my_dense_vector"
        assert service.field_mapping.text_sparse == "my_sparse_vector"
        assert service.field_mapping.node_type_field == "my_node_type"
        
        print("✅ 字段映射功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 字段映射测试失败: {e}")
        return False


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔍 测试向后兼容性...")
    
    try:
        from app.service.milvus_service import MilvusFields
        
        # 验证旧的字段常量仍然可用
        assert MilvusFields.ID_FIELD == "id"
        assert MilvusFields.TEXT_DENSE == "text_dense"
        assert MilvusFields.NODE_TYPE_FIELD == "node_type"
        
        print("✅ 向后兼容性正常")
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False


def test_knowledge_graph_service():
    """测试知识图谱服务更新"""
    print("\n🔍 测试知识图谱服务更新...")
    
    try:
        from app.service.knowledge_graph_service import KnowledgeGraphService
        from app.service.milvus_service import MilvusConnectionConfig, MilvusFieldMapping
        
        # 测试1: 默认构造函数
        kg_service1 = KnowledgeGraphService()
        print("✅ 知识图谱服务默认构造函数创建成功")
        
        # 测试2: 使用自定义Milvus配置
        config = MilvusConnectionConfig(
            uri="http://localhost:19530",
            collection_name="test_collection"
        )
        field_mapping = MilvusFieldMapping(id_field="custom_id")
        
        kg_service2 = KnowledgeGraphService(
            milvus_connection_config=config,
            milvus_field_mapping=field_mapping
        )
        print("✅ 知识图谱服务自定义配置创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 知识图谱服务测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 测试Milvus服务重构")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config_classes,
        test_service_creation,
        test_field_mapping,
        test_backward_compatibility,
        test_knowledge_graph_service
    ]
    
    success_count = 0
    total_tests = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                success_count += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！重构成功！")
    else:
        print("⚠️  部分测试失败，请检查代码")
    
    return success_count == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
