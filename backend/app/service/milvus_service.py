from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pymilvus import MilvusClient, AnnSearchRequest, RRFRanker, RRFRanker

from app.base.logger import setup_logger
from app.utils.config_manager import config

# 使用新的日志配置
logger = setup_logger("milvus_client")


@dataclass
class MilvusConnectionConfig:
    """Milvus连接配置数据类"""
    uri: str
    token: Optional[str] = None
    collection_name: str = "default_collection"
    dimension: Optional[int] = None

    @classmethod
    def from_config_manager(cls, config_manager=None) -> 'MilvusConnectionConfig':
        """从配置管理器创建连接配置"""
        if config_manager is None:
            config_manager = config

        milvus_config = config_manager.milvus_config
        return cls(
            uri=milvus_config.get("uri", ""),
            token=milvus_config.get("token"),
            collection_name=milvus_config.get("collection_name", "default_collection"),
            dimension=milvus_config.get("dimension")
        )


@dataclass
class MilvusFieldMapping:
    """
    Milvus字段映射配置

    支持两种使用方式：
    1. 预定义字段：为常用字段提供默认映射
    2. 自定义字段：通过 custom_fields 字典添加任意字段映射
    """
    # 核心必需字段
    id_field: str = "id"

    # 向量字段（可选）
    dense_vector_field: Optional[str] = None
    sparse_vector_field: Optional[str] = None

    # 常用业务字段（可选，根据实际collection设置）
    content_field: Optional[str] = None
    metadata_fields: Dict[str, str] = None  # 元数据字段映射

    # 自定义字段映射（支持任意字段）
    custom_fields: Dict[str, str] = None

    def __post_init__(self):
        """初始化后处理"""
        if self.metadata_fields is None:
            self.metadata_fields = {}
        if self.custom_fields is None:
            self.custom_fields = {}

    def get_field(self, logical_name: str) -> Optional[str]:
        """
        根据逻辑字段名获取实际字段名

        Args:
            logical_name: 逻辑字段名（如 'id', 'dense_vector', 'content' 等）

        Returns:
            实际字段名，如果未找到返回None
        """
        # 检查核心字段
        if logical_name == "id":
            return self.id_field
        elif logical_name == "dense_vector":
            return self.dense_vector_field
        elif logical_name == "sparse_vector":
            return self.sparse_vector_field
        elif logical_name == "content":
            return self.content_field

        # 检查元数据字段
        if logical_name in self.metadata_fields:
            return self.metadata_fields[logical_name]

        # 检查自定义字段
        if logical_name in self.custom_fields:
            return self.custom_fields[logical_name]

        return None

    def set_field(self, logical_name: str, actual_name: str):
        """
        设置字段映射

        Args:
            logical_name: 逻辑字段名
            actual_name: 实际字段名
        """
        if logical_name == "id":
            self.id_field = actual_name
        elif logical_name == "dense_vector":
            self.dense_vector_field = actual_name
        elif logical_name == "sparse_vector":
            self.sparse_vector_field = actual_name
        elif logical_name == "content":
            self.content_field = actual_name
        else:
            # 其他字段放入自定义字段映射
            self.custom_fields[logical_name] = actual_name

    def get_all_fields(self) -> Dict[str, str]:
        """获取所有字段映射"""
        fields = {"id": self.id_field}

        if self.dense_vector_field:
            fields["dense_vector"] = self.dense_vector_field
        if self.sparse_vector_field:
            fields["sparse_vector"] = self.sparse_vector_field
        if self.content_field:
            fields["content"] = self.content_field

        fields.update(self.metadata_fields)
        fields.update(self.custom_fields)

        return fields


# 保持向后兼容的字段常量定义
class MilvusFields:
    """向后兼容的字段常量定义（已弃用，建议使用 MilvusFieldMapping）"""
    ID_FIELD = "id"
    TEXT_DENSE = "text_dense"
    TEXT_SPARSE = "text_sparse"
    NODE_TYPE_FIELD = "node_type"
    FULL_NAME_FIELD = "full_name"
    DIGEST = "digest"
    CONTENT = "content"
    CREATED_AT_FIELD = "created_at"
    UPDATED_AT_FIELD = "updated_at"
    REPO_ID_FIELD = "repo_id"
    BRANCH_NAME_FIELD = "branch_name"

class MilvusService:
    """
    Milvus向量数据库服务类，提供向量检索和查询功能

    支持两种初始化方式：
    1. 传入配置对象和字段映射（推荐）
    2. 使用默认配置管理器（向后兼容）
    """
    def __init__(self,
                 connection_config: Optional[MilvusConnectionConfig] = None,
                 field_mapping: Optional[MilvusFieldMapping] = None):
        """
        初始化Milvus服务

        Args:
            connection_config: Milvus连接配置，如果为None则使用默认配置
            field_mapping: 字段映射配置，如果为None则使用默认映射
        """
        self.client = None
        self.collection_name = None
        self.connected = False
        self.error_message = None

        # 设置连接配置
        self.connection_config = connection_config

        # 设置字段映射
        self.field_mapping = field_mapping or MilvusFieldMapping()

    def connect(self,
                uri: Optional[str] = None,
                token: Optional[str] = None,
                collection_name: Optional[str] = None,
                dimension: Optional[int] = None):
        """
        连接到Milvus向量数据库

        Args:
            uri: Milvus服务器URI，优先级：参数 > 配置对象 > 默认配置
            token: 连接令牌，优先级：参数 > 配置对象 > 默认配置
            collection_name: 集合名称，优先级：参数 > 配置对象 > 默认配置
            dimension: 向量维度，优先级：参数 > 配置对象 > 默认配置

        Returns:
            bool: 连接是否成功
        """
        try:
            # 优先级：方法参数 > 配置对象 > 默认配置管理器
            if self.connection_config:
                uri = uri or self.connection_config.uri
                token = token or self.connection_config.token
                collection_name = collection_name or self.connection_config.collection_name
                dimension = dimension or self.connection_config.dimension
            else:
                # 向后兼容：使用默认配置管理器
                uri = uri or config.milvus_config.get("uri")
                token = token or config.milvus_config.get("token")
                collection_name = collection_name or config.milvus_config.get("collection_name")
                dimension = dimension or config.milvus_config.get("dimension")

            logger.info(f"正在连接到Milvus: {uri}, 集合: {collection_name}")

            # 连接到Milvus
            self.client = MilvusClient(
                uri=uri,
                token=token
            )

            # 检查集合是否存在
            collections = self.client.list_collections()
            if collection_name not in collections:
                logger.error(f"集合 '{collection_name}' 不存在")
                self.error_message = f"集合 '{collection_name}' 不存在"
                return False

            self.collection_name = collection_name
            self.connected = True
            self.error_message = None

            logger.info(f"成功连接到Milvus: 集合 {collection_name}")
            return True

        except Exception as e:
            logger.exception(f"连接Milvus失败: {str(e)}")
            self.error_message = f"连接异常: {str(e)}"
            self.client = None
            self.connected = False
            return False

    def disconnect(self):
        """断开Milvus连接"""
        if self.client:
            logger.info("断开Milvus数据库连接")
            self.client = None
            self.connected = False

    def search_by_vector(self, vector, limit=10, filter_expr=None, output_fields=None):
        """
        根据向量搜索最相似的节点

        Args:
            vector: 输入向量
            limit: 返回结果数量
            filter_expr: 过滤表达式
            output_fields: 输出字段列表

        Returns:
            搜索结果或None
        """
        if not self.connected or not self.client:
            logger.error("未连接到Milvus，无法执行搜索")
            return None

        try:
            # 设置默认输出字段（使用字段映射）
            if output_fields is None:
                output_fields = [self.field_mapping.id_field]

                # 添加可用的字段
                if self.field_mapping.content_field:
                    output_fields.append(self.field_mapping.content_field)

                # 添加元数据字段
                output_fields.extend(self.field_mapping.metadata_fields.values())

                # 如果没有其他字段，使用一些常见的默认字段名
                if len(output_fields) == 1:  # 只有id字段
                    common_fields = ["content", "metadata", "text"]
                    for field in common_fields:
                        if self.field_mapping.get_field(field):
                            output_fields.append(self.field_mapping.get_field(field))

            # 执行向量搜索
            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10},
                "hints": "iterative_filter"
            }

            logger.debug(f"执行向量搜索，集合: {self.collection_name}, 限制: {limit}, 过滤: {filter_expr}")

            # 获取稠密向量字段名
            dense_field = self.field_mapping.get_field("dense_vector") or self.field_mapping.dense_vector_field
            if not dense_field:
                # 如果没有配置稠密向量字段，尝试使用默认名称
                dense_field = "text_dense"  # 向后兼容
                logger.warning(f"未配置稠密向量字段，使用默认字段名: {dense_field}")

            results = self.client.search(
                collection_name=self.collection_name,
                data=[vector],
                anns_field=dense_field,
                limit=limit,
                output_fields=output_fields,
                filter=filter_expr
                # search_params=search_params
            )

            logger.info(f"向量搜索完成，结果数: {len(results) if results else 0}")
            return results

        except Exception as e:
            logger.exception(f"向量搜索失败: {str(e)}")
            return None

    def hybrid_search(self, query_text, query_dense_vector, limit=10, filter_expr=None, output_fields=None,
                     distance_threshold=None, max_distance=None):
        """
        执行混合检索，结合稠密向量和稀疏文本搜索

        Args:
            query_text: 查询文本
            query_dense_vector: 查询的稠密向量
            limit: 每个搜索请求返回的结果数量
            filter_expr: 过滤表达式
            output_fields: 输出字段列表
            distance_threshold: 距离阈值，用于过滤相似度过低的结果
            max_distance: 最大距离，用于过滤相似度过高的结果

        Returns:
            混合搜索结果或None
        """
        if not self.connected or not self.client:
            logger.error("未连接到Milvus，无法执行混合搜索")
            return None

        try:
            # 设置默认输出字段（使用字段映射）
            if output_fields is None:
                output_fields = [self.field_mapping.id_field]

                # 添加可用的字段
                if self.field_mapping.content_field:
                    output_fields.append(self.field_mapping.content_field)

                # 添加所有元数据字段
                output_fields.extend(self.field_mapping.metadata_fields.values())

                # 添加自定义字段
                output_fields.extend(self.field_mapping.custom_fields.values())

            # 构建稠密向量搜索请求的参数
            dense_param = {"nprobe": 10}

            # 添加距离过滤参数（适用于COSINE距离）
            if distance_threshold is not None:
                dense_param["radius"] = distance_threshold
            if max_distance is not None:
                dense_param["range_filter"] = max_distance

            # 获取稠密向量字段名
            dense_field = self.field_mapping.get_field("dense_vector") or self.field_mapping.dense_vector_field
            if not dense_field:
                dense_field = "text_dense"  # 向后兼容
                logger.warning(f"未配置稠密向量字段，使用默认字段名: {dense_field}")

            search_param_1 = {
                "data": [query_dense_vector],
                "anns_field": dense_field,
                "param": dense_param,
                "limit": limit,
                "expr": filter_expr,
                "expr_params": {"hints": "iterative_filter"}
            }
            request_1 = AnnSearchRequest(**search_param_1)

            # 构建稀疏文本搜索请求的参数
            sparse_param = {"drop_ratio_search": 0.2}

            # 稀疏向量搜索也可以添加距离过滤
            if distance_threshold is not None:
                sparse_param["radius"] = distance_threshold
            if max_distance is not None:
                sparse_param["range_filter"] = max_distance

            # 获取稀疏向量字段名
            sparse_field = self.field_mapping.get_field("sparse_vector") or self.field_mapping.sparse_vector_field
            if not sparse_field:
                sparse_field = "text_sparse"  # 向后兼容
                logger.warning(f"未配置稀疏向量字段，使用默认字段名: {sparse_field}")

            search_param_2 = {
                "data": [query_text],
                "anns_field": sparse_field,
                "param": sparse_param,
                "limit": limit,
                "expr": filter_expr,
                "expr_params": {"hints": "iterative_filter"}
            }
            request_2 = AnnSearchRequest(**search_param_2)

            # 组合搜索请求
            reqs = [request_1, request_2]

            logger.debug(f"执行混合搜索，集合: {self.collection_name}, 限制: {limit}, 过滤: {filter_expr}")

            ranker = RRFRanker(100)

            # 执行混合搜索
            results = self.client.hybrid_search(
                collection_name=self.collection_name,
                reqs=reqs,
                ranker=ranker,
                limit=limit,
                output_fields=output_fields
            )

            logger.info(f"混合搜索完成，结果数: {len(results) if results else 0}")
            return results

        except Exception as e:
            logger.error(f"混合搜索失败: {type(e).__name__}: {str(e)}")
            logger.exception("混合搜索详细错误信息:")
            # 如果混合搜索失败，回退到稠密向量搜索
            logger.warning("混合搜索失败，回退到稠密向量搜索")
            try:
                return self.search_by_vector(
                    vector=query_dense_vector,
                    limit=limit,
                    filter_expr=filter_expr,
                    output_fields=output_fields
                )
            except Exception as fallback_error:
                logger.error(f"回退搜索也失败: {type(fallback_error).__name__}: {str(fallback_error)}")
                return None

    def search_by_text(self, search_text, node_types=None, limit=10, embedding_function=None):
        """
        根据文本搜索最相似的节点
        
        Args:
            search_text: 搜索文本
            node_types: 节点类型列表，用于过滤结果
            limit: 返回结果数量
            embedding_function: 嵌入函数，将文本转换为向量
            
        Returns:
            搜索结果或None
        """
        if not self.connected or not self.client:
            logger.error("未连接到Milvus，无法执行搜索")
            return None

        try:
            # 确保提供了嵌入函数
            if embedding_function is None:
                logger.error("未提供嵌入函数，无法将文本转换为向量")
                return None

            # 将搜索文本转换为向量
            vector = embedding_function(search_text)

            # 构建过滤表达式
            filter_expr = None
            if node_types and len(node_types) > 0:
                node_types_str = ",".join([f"'{t}'" for t in node_types])
                filter_expr = f"node_type in [{node_types_str}]"

            # 执行向量搜索
            return self.search_by_vector(
                vector=vector,
                limit=limit,
                filter_expr=filter_expr
            )

        except Exception as e:
            logger.exception(f"文本搜索失败: {str(e)}")
            return None

    def get_error_message(self):
        """获取最后一次错误信息"""
        return getattr(self, 'error_message', "未知错误")


# 便利函数：创建使用默认配置的MilvusService实例
def create_milvus_service_from_config(config_manager=None,
                                     field_mapping: Optional[MilvusFieldMapping] = None) -> MilvusService:
    """
    从配置管理器创建MilvusService实例

    Args:
        config_manager: 配置管理器实例，如果为None则使用默认配置
        field_mapping: 字段映射配置，如果为None则使用默认映射

    Returns:
        MilvusService实例
    """
    connection_config = MilvusConnectionConfig.from_config_manager(config_manager)
    return MilvusService(connection_config=connection_config, field_mapping=field_mapping)


# 便利函数：创建使用默认配置和字段的MilvusService实例（向后兼容）
def create_default_milvus_service() -> MilvusService:
    """
    创建使用默认配置和字段映射的MilvusService实例
    这个函数提供向后兼容性

    Returns:
        MilvusService实例
    """
    return MilvusService()