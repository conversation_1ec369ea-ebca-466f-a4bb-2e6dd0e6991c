"""
Milvus配置示例文件
演示如何使用新的配置方式来初始化MilvusService
"""

from app.service.milvus_service import (
    MilvusService, 
    MilvusConnectionConfig, 
    MilvusFieldMapping,
    create_milvus_service_from_config,
    create_default_milvus_service
)


def create_custom_milvus_service():
    """创建自定义配置的Milvus服务示例"""
    
    # 方式1: 直接创建配置对象
    connection_config = MilvusConnectionConfig(
        uri="http://localhost:19530",
        token="your_token_here",
        collection_name="custom_collection",
        dimension=768
    )
    
    # 自定义字段映射
    field_mapping = MilvusFieldMapping(
        id_field="custom_id",
        text_dense="custom_dense_vector",
        text_sparse="custom_sparse_vector",
        node_type_field="custom_node_type",
        full_name_field="custom_full_name",
        digest="custom_digest",
        content="custom_content",
        created_at_field="custom_created_at",
        updated_at_field="custom_updated_at",
        repo_id_field="custom_repo_id",
        branch_name_field="custom_branch_name"
    )
    
    # 创建服务实例
    milvus_service = MilvusService(
        connection_config=connection_config,
        field_mapping=field_mapping
    )
    
    return milvus_service


def create_milvus_service_from_dict():
    """从字典配置创建Milvus服务示例"""
    
    # 从字典或其他配置源创建配置
    config_dict = {
        "uri": "http://localhost:19530",
        "token": "your_token_here",
        "collection_name": "my_collection",
        "dimension": 1024
    }
    
    connection_config = MilvusConnectionConfig(
        uri=config_dict["uri"],
        token=config_dict.get("token"),
        collection_name=config_dict["collection_name"],
        dimension=config_dict.get("dimension")
    )
    
    # 使用默认字段映射
    milvus_service = MilvusService(connection_config=connection_config)
    
    return milvus_service


def create_milvus_service_with_env_vars():
    """从环境变量创建Milvus服务示例"""
    import os
    
    connection_config = MilvusConnectionConfig(
        uri=os.getenv("MILVUS_URI", "http://localhost:19530"),
        token=os.getenv("MILVUS_TOKEN"),
        collection_name=os.getenv("MILVUS_COLLECTION", "default_collection"),
        dimension=int(os.getenv("MILVUS_DIMENSION", "768"))
    )
    
    milvus_service = MilvusService(connection_config=connection_config)
    
    return milvus_service


def usage_examples():
    """使用示例"""
    
    print("=== Milvus服务配置示例 ===")
    
    # 示例1: 使用默认配置（向后兼容）
    print("\n1. 使用默认配置:")
    default_service = create_default_milvus_service()
    print(f"默认服务创建成功: {type(default_service).__name__}")
    
    # 示例2: 从配置管理器创建
    print("\n2. 从配置管理器创建:")
    config_service = create_milvus_service_from_config()
    print(f"配置服务创建成功: {type(config_service).__name__}")
    
    # 示例3: 自定义配置
    print("\n3. 使用自定义配置:")
    custom_service = create_custom_milvus_service()
    print(f"自定义服务创建成功: {type(custom_service).__name__}")
    
    # 示例4: 从字典配置创建
    print("\n4. 从字典配置创建:")
    dict_service = create_milvus_service_from_dict()
    print(f"字典配置服务创建成功: {type(dict_service).__name__}")
    
    # 示例5: 从环境变量创建
    print("\n5. 从环境变量创建:")
    env_service = create_milvus_service_with_env_vars()
    print(f"环境变量服务创建成功: {type(env_service).__name__}")


if __name__ == "__main__":
    usage_examples()
