"""
Milvus配置示例文件
演示如何使用新的配置方式来初始化MilvusService
"""

from app.service.milvus_service import (
    MilvusService, 
    MilvusConnectionConfig, 
    MilvusFieldMapping,
    create_milvus_service_from_config,
    create_default_milvus_service
)


def create_custom_milvus_service():
    """创建自定义配置的Milvus服务示例"""
    
    # 方式1: 直接创建配置对象
    connection_config = MilvusConnectionConfig(
        uri="http://localhost:19530",
        token="your_token_here",
        collection_name="custom_collection",
        dimension=768
    )
    
    # 自定义字段映射 - 新的灵活方式
    field_mapping = MilvusFieldMapping(
        id_field="custom_id",
        dense_vector_field="custom_dense_vector",
        sparse_vector_field="custom_sparse_vector",
        content_field="custom_content",
        metadata_fields={
            "node_type": "custom_node_type",
            "full_name": "custom_full_name",
            "digest": "custom_digest",
            "created_at": "custom_created_at",
            "updated_at": "custom_updated_at"
        },
        custom_fields={
            "repo_id": "custom_repo_id",
            "branch_name": "custom_branch_name",
            "file_path": "custom_file_path",
            "language": "custom_language"
        }
    )
    
    # 创建服务实例
    milvus_service = MilvusService(
        connection_config=connection_config,
        field_mapping=field_mapping
    )
    
    return milvus_service


def create_milvus_service_from_dict():
    """从字典配置创建Milvus服务示例"""
    
    # 从字典或其他配置源创建配置
    config_dict = {
        "uri": "http://localhost:19530",
        "token": "your_token_here",
        "collection_name": "my_collection",
        "dimension": 1024
    }
    
    connection_config = MilvusConnectionConfig(
        uri=config_dict["uri"],
        token=config_dict.get("token"),
        collection_name=config_dict["collection_name"],
        dimension=config_dict.get("dimension")
    )
    
    # 使用默认字段映射
    milvus_service = MilvusService(connection_config=connection_config)
    
    return milvus_service


def create_milvus_service_with_env_vars():
    """从环境变量创建Milvus服务示例"""
    import os
    
    connection_config = MilvusConnectionConfig(
        uri=os.getenv("MILVUS_URI", "http://localhost:19530"),
        token=os.getenv("MILVUS_TOKEN"),
        collection_name=os.getenv("MILVUS_COLLECTION", "default_collection"),
        dimension=int(os.getenv("MILVUS_DIMENSION", "768"))
    )
    
    milvus_service = MilvusService(connection_config=connection_config)
    
    return milvus_service


def create_document_collection_service():
    """为文档集合创建专门的Milvus服务"""

    connection_config = MilvusConnectionConfig(
        uri="http://localhost:19530",
        collection_name="document_vectors",
        dimension=768
    )

    # 文档集合的字段映射
    field_mapping = MilvusFieldMapping(
        id_field="doc_id",
        dense_vector_field="embedding",
        content_field="document_text",
        metadata_fields={
            "title": "doc_title",
            "author": "doc_author",
            "category": "doc_category",
            "created_time": "created_at"
        },
        custom_fields={
            "file_size": "size_bytes",
            "file_type": "mime_type",
            "language": "detected_lang"
        }
    )

    return MilvusService(connection_config=connection_config, field_mapping=field_mapping)


def create_code_collection_service():
    """为代码集合创建专门的Milvus服务"""

    connection_config = MilvusConnectionConfig(
        uri="http://localhost:19530",
        collection_name="code_vectors",
        dimension=1024
    )

    # 代码集合的字段映射
    field_mapping = MilvusFieldMapping(
        id_field="code_id",
        dense_vector_field="code_embedding",
        sparse_vector_field="keyword_embedding",
        content_field="source_code",
        metadata_fields={
            "function_name": "func_name",
            "class_name": "class_name",
            "file_path": "file_path",
            "language": "prog_language"
        },
        custom_fields={
            "repo_id": "repository_id",
            "branch": "git_branch",
            "commit_hash": "git_commit",
            "complexity": "cyclomatic_complexity"
        }
    )

    return MilvusService(connection_config=connection_config, field_mapping=field_mapping)


def demonstrate_field_access():
    """演示字段访问方法"""

    print("\n=== 字段映射访问示例 ===")

    # 创建一个字段映射
    field_mapping = MilvusFieldMapping(
        id_field="my_id",
        dense_vector_field="my_vector",
        content_field="my_content",
        metadata_fields={
            "title": "doc_title",
            "category": "doc_category"
        },
        custom_fields={
            "priority": "task_priority",
            "status": "task_status"
        }
    )

    # 演示不同的字段访问方式
    print(f"ID字段: {field_mapping.get_field('id')}")
    print(f"向量字段: {field_mapping.get_field('dense_vector')}")
    print(f"内容字段: {field_mapping.get_field('content')}")
    print(f"标题字段: {field_mapping.get_field('title')}")
    print(f"优先级字段: {field_mapping.get_field('priority')}")

    # 动态添加字段
    field_mapping.set_field("new_field", "actual_new_field")
    print(f"新添加的字段: {field_mapping.get_field('new_field')}")

    # 获取所有字段映射
    all_fields = field_mapping.get_all_fields()
    print(f"所有字段映射: {all_fields}")


def usage_examples():
    """使用示例"""

    print("=== Milvus服务配置示例 ===")

    # 示例1: 使用默认配置（向后兼容）
    print("\n1. 使用默认配置:")
    default_service = create_default_milvus_service()
    print(f"默认服务创建成功: {type(default_service).__name__}")

    # 示例2: 从配置管理器创建
    print("\n2. 从配置管理器创建:")
    config_service = create_milvus_service_from_config()
    print(f"配置服务创建成功: {type(config_service).__name__}")

    # 示例3: 自定义配置
    print("\n3. 使用自定义配置:")
    custom_service = create_custom_milvus_service()
    print(f"自定义服务创建成功: {type(custom_service).__name__}")

    # 示例4: 从字典配置创建
    print("\n4. 从字典配置创建:")
    dict_service = create_milvus_service_from_dict()
    print(f"字典配置服务创建成功: {type(dict_service).__name__}")

    # 示例5: 从环境变量创建
    print("\n5. 从环境变量创建:")
    env_service = create_milvus_service_with_env_vars()
    print(f"环境变量服务创建成功: {type(env_service).__name__}")

    # 示例6: 文档集合专用服务
    print("\n6. 文档集合专用服务:")
    doc_service = create_document_collection_service()
    print(f"文档服务创建成功: {type(doc_service).__name__}")

    # 示例7: 代码集合专用服务
    print("\n7. 代码集合专用服务:")
    code_service = create_code_collection_service()
    print(f"代码服务创建成功: {type(code_service).__name__}")

    # 示例8: 字段访问演示
    demonstrate_field_access()


if __name__ == "__main__":
    usage_examples()
