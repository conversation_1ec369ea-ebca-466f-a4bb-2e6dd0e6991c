# 文档查询MCP接口使用指南

## 概述

新增了两个MCP接口用于查询向量数据库中的文档说明书：

1. **查询聚合说明书接口** (`query_aggregated_documentation`)
2. **查询流程说明书接口** (`query_process_documentation`)

## 数据库配置

### Collection信息
- **Collection名称**: `documentation_vectors`
- **数据库地址**: 与现有代码一致（从配置文件读取）

### 字段结构

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `document_id` | VarChar(32) | 说明书的唯一标识符（主键） |
| `content` | VarChar(65535) | 说明书的摘要内容 |
| `document_type` | VarChar(128) | 说明书类型（document-chain-1/2/3, document-flow） |
| `text_dense` | FloatVector | 稠密向量表示 |
| `text_sparse` | SparseFloatVector | 稀疏向量表示（BM25） |
| `created_at` | Int64 | 创建时间（毫秒时间戳） |
| `updated_at` | Int64 | 更新时间（毫秒时间戳） |
| `repo_id` | VarChar(256) | 仓库ID |
| `branch_name` | VarChar(256) | 分支名称 |

## MCP接口详情

### 1. 查询聚合说明书接口

**工具名称**: `query_aggregated_documentation`

**功能**: 根据查询文本搜索一级链路说明书，返回最相似的文档内容

**参数**:
```json
{
  "repo_id": "仓库ID",
  "branch_name": "分支名称", 
  "original_user_query_text": "原始用户查询文本",
  "query_text": "处理后的查询文本",
  "topk": 5
}
```

**返回结果**:
```json
{
  "original_query": "原始用户查询文本",
  "processed_query": "处理后的查询文本", 
  "document_id": "1234567890123456789",
  "content": "说明书内容...",
  "score": 0.85,
  "document_type": "document-chain-1",
  "all_results": [
    {
      "document_id": "1234567890123456789",
      "score": 0.85,
      "document_type": "document-chain-1",
      "content_preview": "内容预览..."
    }
  ]
}
```

**逻辑流程**:
1. 使用 `query_text` 生成向量
2. 在向量数据库中搜索，过滤条件：
   - `repo_id == 指定仓库ID`
   - `branch_name == 指定分支`
   - `document_type == "document-chain-1"`
3. 返回topk个最相似的结果
4. 调用外部API获取最相似文档的完整内容

### 2. 查询流程说明书接口

**工具名称**: `query_process_documentation`

**功能**: 根据文档ID获取流程说明书内容和关联方法信息

**参数**:
```json
{
  "repo_id": "仓库ID",
  "branch_name": "分支名称",
  "document_id": "1234567890123456789"
}
```

**返回结果**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "id": "210399923378769920",
    "content": "说明书内容...",
    "entryPointId": "afa3ffc95a82a544a34d1411d5f0b9b0",
    "entryPointName": "入口点名称"
  }
}
```

**逻辑流程**:
1. 直接调用外部API获取流程文档内容
2. 返回文档内容和关联的方法信息

## 配置说明

在 `config.properties` 中添加文档服务配置：

```properties
# 文档服务配置
[DOCUMENTATION]
host = localhost
port = 8080
api_base = /documentation/api
```

## 外部API接口

### 聚合文档API
- **URL**: `http://{host}:{port}{api_base}/documentation/query/aggregated/{document_id}`
- **方法**: GET
- **示例**: `http://localhost:8080/documentation/api/documentation/query/aggregated/1234567890123456789`

### 流程文档API  
- **URL**: `http://{host}:{port}{api_base}/documentation/query/process/{document_id}`
- **方法**: GET
- **示例**: `http://localhost:8080/documentation/api/documentation/query/process/1234567890123456789`

## 使用示例

### 示例1: 查询聚合说明书

```python
# MCP工具调用
result = mcp_client.call_tool("query_aggregated_documentation", {
    "repo_id": "*************:whrdckf3/maling-query.git",
    "branch_name": "main",
    "original_user_query_text": "用户登录流程",
    "query_text": "用户登录认证流程说明",
    "topk": 3
})

print(f"找到文档: {result['document_id']}")
print(f"内容: {result['content'][:200]}...")
```

### 示例2: 查询流程说明书

```python
# MCP工具调用
result = mcp_client.call_tool("query_process_documentation", {
    "repo_id": "*************:whrdckf3/maling-query.git",
    "branch_name": "main",
    "document_id": "1234567890123456789"
})

if result['success']:
    data = result['data']
    print(f"流程文档: {data['content'][:200]}...")
    print(f"入口点: {data['entryPointName']}")
    print(f"文档ID: {data['id']}")
else:
    print(f"查询失败: {result['message']}")
```

## 错误处理

接口会返回详细的错误信息：

```json
{
  "error": "错误描述信息"
}
```

常见错误：
- 文档服务初始化失败
- 向量数据库连接失败
- 外部API调用失败
- 未找到匹配的文档

## 技术实现

### 核心组件

1. **DocumentationService**: 文档服务类
   - 管理向量数据库连接
   - 执行向量搜索
   - 调用外部API

2. **MilvusService**: 向量数据库服务
   - 使用新的依赖注入配置方式
   - 支持不同collection的字段结构

3. **MCP工具**: FastMCP框架集成
   - 注册为MCP工具
   - 提供标准化的接口

### 依赖关系

```
MCP接口 -> DocumentationService -> MilvusService -> Milvus数据库
         -> HTTP Client -> 外部文档API
```

## 部署注意事项

1. **向量数据库**: 确保 `documentation_vectors` collection已创建并包含数据
2. **外部API**: 确保文档服务API可访问
3. **配置文件**: 正确配置数据库连接和API地址
4. **网络连接**: 确保服务间网络连通性

## 监控和日志

所有操作都会记录详细日志：
- 搜索请求和结果
- API调用状态
- 错误信息和异常堆栈

日志级别可通过配置调整，便于调试和监控。
