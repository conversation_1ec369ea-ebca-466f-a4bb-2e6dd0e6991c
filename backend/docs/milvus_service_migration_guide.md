# Milvus服务重构迁移指南

## 概述

本文档描述了 `MilvusService` 类的重构，将硬编码的连接配置和字段定义改为由上游传入的依赖注入模式。

## 重构目标

1. **解耦配置依赖**：移除对 `config.milvus_config` 的硬编码依赖
2. **灵活字段映射**：支持动态字段映射而不是硬编码字段名
3. **依赖注入**：通过构造函数传入配置和字段映射
4. **向后兼容**：保持现有API的兼容性

## 主要变更

### 1. 新增配置数据类

```python
@dataclass
class MilvusConnectionConfig:
    """Milvus连接配置数据类"""
    uri: str
    token: Optional[str] = None
    collection_name: str = "default_collection"
    dimension: Optional[int] = None
    
    @classmethod
    def from_config_manager(cls, config_manager=None) -> 'MilvusConnectionConfig':
        """从配置管理器创建连接配置"""
        # 实现细节...

@dataclass
class MilvusFieldMapping:
    """
    Milvus字段映射配置 - 灵活支持不同collection的字段结构

    支持两种使用方式：
    1. 预定义字段：为常用字段提供默认映射
    2. 自定义字段：通过 custom_fields 字典添加任意字段映射
    """
    # 核心必需字段
    id_field: str = "id"

    # 向量字段（可选）
    dense_vector_field: Optional[str] = None
    sparse_vector_field: Optional[str] = None

    # 常用业务字段（可选）
    content_field: Optional[str] = None
    metadata_fields: Dict[str, str] = None  # 元数据字段映射

    # 自定义字段映射（支持任意字段）
    custom_fields: Dict[str, str] = None
```

### 2. 重构MilvusService构造函数

```python
class MilvusService:
    def __init__(self, 
                 connection_config: Optional[MilvusConnectionConfig] = None,
                 field_mapping: Optional[MilvusFieldMapping] = None):
        """
        初始化Milvus服务
        
        Args:
            connection_config: Milvus连接配置，如果为None则使用默认配置
            field_mapping: 字段映射配置，如果为None则使用默认映射
        """
```

### 3. 更新字段引用

所有硬编码的 `MilvusFields.FIELD_NAME` 引用都改为使用 `self.field_mapping.field_name`。

## 迁移方式

### 方式1：无需修改（向后兼容）

现有代码无需修改，继续使用默认配置：

```python
# 旧代码 - 仍然有效
milvus_service = MilvusService()
milvus_service.connect()
```

### 方式2：使用便利函数

```python
# 使用便利函数从配置管理器创建
from app.service.milvus_service import create_milvus_service_from_config

milvus_service = create_milvus_service_from_config()
milvus_service.connect()
```

### 方式3：自定义配置（推荐）

```python
from app.service.milvus_service import (
    MilvusService, 
    MilvusConnectionConfig, 
    MilvusFieldMapping
)

# 创建自定义连接配置
connection_config = MilvusConnectionConfig(
    uri="http://localhost:19530",
    token="your_token",
    collection_name="my_collection",
    dimension=768
)

# 创建自定义字段映射
field_mapping = MilvusFieldMapping(
    id_field="custom_id",
    text_dense="custom_dense_vector",
    # 其他字段...
)

# 创建服务实例
milvus_service = MilvusService(
    connection_config=connection_config,
    field_mapping=field_mapping
)

# 连接时不需要传参数，会使用配置对象中的值
milvus_service.connect()
```

### 方式4：从环境变量创建

```python
import os
from app.service.milvus_service import MilvusService, MilvusConnectionConfig

connection_config = MilvusConnectionConfig(
    uri=os.getenv("MILVUS_URI", "http://localhost:19530"),
    token=os.getenv("MILVUS_TOKEN"),
    collection_name=os.getenv("MILVUS_COLLECTION", "default_collection"),
    dimension=int(os.getenv("MILVUS_DIMENSION", "768"))
)

milvus_service = MilvusService(connection_config=connection_config)
```

## KnowledgeGraphService 更新

`KnowledgeGraphService` 也支持传入自定义Milvus配置：

```python
from app.service.knowledge_graph_service import KnowledgeGraphService
from app.service.milvus_service import MilvusConnectionConfig, MilvusFieldMapping

# 创建自定义配置
connection_config = MilvusConnectionConfig(...)
field_mapping = MilvusFieldMapping(...)

# 传入知识图谱服务
kg_service = KnowledgeGraphService(
    milvus_connection_config=connection_config,
    milvus_field_mapping=field_mapping
)
```

## 优势

1. **灵活性**：支持多种配置来源（配置文件、环境变量、代码配置等）
2. **可测试性**：便于单元测试时注入模拟配置
3. **解耦**：服务类不再依赖特定的配置管理器
4. **可扩展性**：易于添加新的配置选项和字段映射
5. **向后兼容**：现有代码无需修改

## 注意事项

1. `MilvusFields` 类仍然保留用于向后兼容，但建议使用 `MilvusFieldMapping`
2. 连接参数的优先级：方法参数 > 配置对象 > 默认配置管理器
3. 如果需要在运行时动态修改配置，建议创建新的服务实例

## 示例代码

完整的使用示例请参考 `backend/app/config/milvus_config_example.py` 文件。
