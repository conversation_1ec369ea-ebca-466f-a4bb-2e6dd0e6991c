# Milvus服务重构迁移指南

## 概述

本文档描述了 `MilvusService` 类的重构，将硬编码的连接配置和字段定义改为由上游传入的依赖注入模式。

## 重构目标

1. **解耦配置依赖**：移除对 `config.milvus_config` 的硬编码依赖
2. **灵活字段配置**：支持不同collection的字段结构
3. **依赖注入**：通过构造函数传入配置和字段配置
4. **简化设计**：去除复杂的兼容逻辑，直接要求上游传入配置

## 主要变更

### 1. 新增配置数据类

```python
@dataclass
class MilvusConnectionConfig:
    """Milvus连接配置数据类"""
    uri: str
    token: Optional[str] = None
    collection_name: str = "default_collection"
    dimension: Optional[int] = None

@dataclass
class MilvusFieldConfig:
    """Milvus字段配置 - 简单直接的字段映射"""
    id_field: str
    dense_vector_field: Optional[str] = None
    sparse_vector_field: Optional[str] = None
    output_fields: List[str] = None  # 默认输出字段列表
```

### 2. 重构MilvusService构造函数

```python
class MilvusService:
    def __init__(self,
                 connection_config: MilvusConnectionConfig,
                 field_config: MilvusFieldConfig):
        """
        初始化Milvus服务

        Args:
            connection_config: Milvus连接配置（必需）
            field_config: 字段配置（必需）
        """
```

### 3. 更新字段引用

所有硬编码的字段名都改为使用配置对象中的字段名。

## 迁移方式

**注意：此重构不保持向后兼容性，所有使用 MilvusService 的代码都需要更新。**

### 新的使用方式（必需）

```python
from app.service.milvus_service import (
    MilvusService,
    MilvusConnectionConfig,
    MilvusFieldConfig
)

# 创建连接配置
connection_config = MilvusConnectionConfig(
    uri="http://localhost:19530",
    token="your_token",
    collection_name="my_collection",
    dimension=768
)

# 创建字段配置
field_config = MilvusFieldConfig(
    id_field="custom_id",
    dense_vector_field="custom_dense_vector",
    sparse_vector_field="custom_sparse_vector",
    output_fields=["custom_id", "content", "title", "category"]
)

# 创建服务实例
milvus_service = MilvusService(
    connection_config=connection_config,
    field_config=field_config
)

# 连接时不需要传参数，会使用配置对象中的值
milvus_service.connect()
```

### 从环境变量创建

```python
import os
from app.service.milvus_service import MilvusService, MilvusConnectionConfig, MilvusFieldConfig

connection_config = MilvusConnectionConfig(
    uri=os.getenv("MILVUS_URI", "http://localhost:19530"),
    token=os.getenv("MILVUS_TOKEN"),
    collection_name=os.getenv("MILVUS_COLLECTION", "default_collection"),
    dimension=int(os.getenv("MILVUS_DIMENSION", "768"))
)

field_config = MilvusFieldConfig(
    id_field=os.getenv("MILVUS_ID_FIELD", "id"),
    dense_vector_field=os.getenv("MILVUS_VECTOR_FIELD", "embedding"),
    output_fields=os.getenv("MILVUS_OUTPUT_FIELDS", "id,content").split(",")
)

milvus_service = MilvusService(
    connection_config=connection_config,
    field_config=field_config
)
```

## KnowledgeGraphService 更新

`KnowledgeGraphService` 现在要求传入Milvus配置：

```python
from app.service.knowledge_graph_service import KnowledgeGraphService
from app.service.milvus_service import MilvusConnectionConfig, MilvusFieldConfig

# 创建配置
connection_config = MilvusConnectionConfig(
    uri="http://localhost:19530",
    collection_name="node_vectors",
    dimension=1024
)

field_config = MilvusFieldConfig(
    id_field="id",
    dense_vector_field="text_dense",
    sparse_vector_field="text_sparse",
    output_fields=["id", "node_type", "full_name", "digest"]
)

# 创建知识图谱服务
kg_service = KnowledgeGraphService(
    milvus_connection_config=connection_config,
    milvus_field_config=field_config
)
```

## 优势

1. **简洁明了**：直接传入配置，无复杂的兼容逻辑
2. **灵活性**：支持不同collection的字段结构
3. **可测试性**：便于单元测试时注入模拟配置
4. **解耦**：服务类不再依赖特定的配置管理器
5. **类型安全**：使用数据类提供类型检查

## 注意事项

1. **不向后兼容**：所有使用 MilvusService 的代码都需要更新
2. **必需参数**：连接配置和字段配置都是必需的
3. **字段配置**：需要根据实际collection的字段结构配置 output_fields

## 示例代码

完整的使用示例请参考 `backend/app/config/milvus_config_example.py` 文件。
